import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";

export class PropertyModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "property";
    static async Get(id: string) {
        return await JC_Get<PropertyModel>(this.apiRoute, { id }, PropertyModel);
    }
    static async GetList(paging?:JC_ListPagingModel) {
        return await JC_GetList<PropertyModel>(`${this.apiRoute}/getList`, PropertyModel, paging, {});
    }
    static async Create(data: PropertyModel) {
        let response = await JC_Put<PropertyModel>(this.apiRoute, data);
        return response;
    }
    static async CreateList(dataList: PropertyModel[]) {
        return await JC_Put<PropertyModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: PropertyModel) {
        return await JC_Post<PropertyModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: PropertyModel[]) {
        return await JC_Post<PropertyModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(id: string) {
        return await JC_Delete(this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { ids });
    }


    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Address: string;
    BuildingTypeCode?: string;
    CompanyStrataTitleCode?: string;
    NumBedroomsCode?: string;
    OrientationCode?: string;
    StoreysCode?: string;
    FurnishedCode?: string;
    OccupiedCode?: string;
    FloorCode?: string;
    OtherBuildingElementsCode?: string;
    OtherTimberBldgElementsCode?: string;
    RoofCode?: string;
    WallsCode?: string;
    WeatherCode?: string;
    RoomsListJson?: string;
    SortOrder: number;


    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<PropertyModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.Address = "";
        this.BuildingTypeCode = undefined;
        this.CompanyStrataTitleCode = undefined;
        this.NumBedroomsCode = undefined;
        this.OrientationCode = undefined;
        this.StoreysCode = undefined;
        this.FurnishedCode = undefined;
        this.OccupiedCode = undefined;
        this.FloorCode = undefined;
        this.OtherBuildingElementsCode = undefined;
        this.OtherTimberBldgElementsCode = undefined;
        this.RoofCode = undefined;
        this.WallsCode = undefined;
        this.WeatherCode = undefined;
        this.RoomsListJson = undefined;
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new PropertyModel());
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Address} | ${this.CompanyStrataTitleCode}`;
    }

}
